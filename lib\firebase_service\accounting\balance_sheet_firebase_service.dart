import 'dart:developer';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:either_dart/either.dart';
import '../../core/shared_services/failure_obj.dart';
import '../../core/shared_services/success_obj.dart';
import '../../models/accounting/financial_report_models.dart';
import '../../models/finance/chart_of_accounts_model.dart';
import '../../models/finance/journal_entry_model.dart';
import '../../core/services/account_type_helper_service.dart';

/// Firebase service for Balance Sheet report operations
class BalanceSheetFirebaseService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  /// Generate Balance Sheet report
  Future<Either<FailureObj, BalanceSheetReport>> generateBalanceSheet({
    required DateTime asOfDate,
    required String companyName,
    bool includeInactiveAccounts = false,
    bool includeZeroBalances = false,
  }) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return Left(FailureObj(
          code: 'user-not-authenticated',
          message: 'User not authenticated',
        ));
      }

      log('Generating Balance Sheet as of $asOfDate');

      // Get all accounts
      final accountsQuery = _firestore
          .collection('chart_of_accounts')
          .where('uid', isEqualTo: user.uid);

      final accountsSnapshot = await accountsQuery.get();
      final accounts = accountsSnapshot.docs
          .map((doc) => ChartOfAccountsModel.fromJson({
                'id': doc.id,
                ...doc.data(),
              }))
          .where((account) => includeInactiveAccounts || account.isActive)
          .toList();

      // Group accounts by category
      final assetAccounts = accounts
          .where((account) => account.category == AccountCategory.assets)
          .toList();
      final liabilityAccounts = accounts
          .where((account) => account.category == AccountCategory.liabilities)
          .toList();
      final equityAccounts = accounts
          .where((account) => account.category == AccountCategory.equity)
          .toList();

      // Calculate balances and create groups
      final assetGroups = await _createAssetGroups(
          assetAccounts, asOfDate, includeZeroBalances);
      final liabilityGroups = await _createLiabilityGroups(
          liabilityAccounts, asOfDate, includeZeroBalances);
      final equityGroups = await _createEquityGroups(
          equityAccounts, asOfDate, includeZeroBalances);

      // Calculate totals
      final totalAssets =
          assetGroups.fold(0.0, (total, group) => total + group.groupTotal);
      final totalLiabilities =
          liabilityGroups.fold(0.0, (total, group) => total + group.groupTotal);
      final totalEquity =
          equityGroups.fold(0.0, (total, group) => total + group.groupTotal);

      // Create report
      final report = BalanceSheetReport(
        reportId: _generateReportId(),
        companyName: companyName,
        uid: user.uid,
        startDate:
            DateTime(asOfDate.year, 1, 1), // Start of year for balance sheet
        endDate: asOfDate,
        generatedAt: DateTime.now(),
        generatedBy: user.email ?? 'Unknown',
        assetGroups: assetGroups,
        liabilityGroups: liabilityGroups,
        equityGroups: equityGroups,
        totalAssets: totalAssets,
        totalLiabilities: totalLiabilities,
        totalEquity: totalEquity,
      );

      log('Balance Sheet generated successfully');
      return Right(report);
    } catch (e) {
      log('Error generating Balance Sheet: $e');
      return Left(FailureObj(
        code: 'balance-sheet-generation-error',
        message: 'Failed to generate Balance Sheet: $e',
      ));
    }
  }

  /// Create asset groups with current/non-current classification
  Future<List<BSAccountGroup>> _createAssetGroups(
    List<ChartOfAccountsModel> assetAccounts,
    DateTime asOfDate,
    bool includeZeroBalances,
  ) async {
    final groups = <BSAccountGroup>[];

    // Group by account type
    final currentAssets = <BSAccountItem>[];
    final nonCurrentAssets = <BSAccountItem>[];

    for (final account in assetAccounts) {
      final balance = await _calculateAccountBalance(account.id, asOfDate);

      if (!includeZeroBalances && balance == 0) continue;

      final accountItem = BSAccountItem(
        accountId: account.id,
        accountNumber: account.accountNumber,
        accountName: account.accountName,
        amount: balance,
      );

      // Classify as current or non-current based on account type
      if (_isCurrentAsset(account.accountType)) {
        currentAssets.add(accountItem);
      } else {
        nonCurrentAssets.add(accountItem);
      }
    }

    // Create groups
    if (currentAssets.isNotEmpty) {
      groups.add(BSAccountGroup(
        groupName: 'Current Assets',
        groupType: 'current_assets',
        accounts: currentAssets,
        groupTotal:
            currentAssets.fold(0.0, (total, item) => total + item.amount),
      ));
    }

    if (nonCurrentAssets.isNotEmpty) {
      groups.add(BSAccountGroup(
        groupName: 'Non-Current Assets',
        groupType: 'non_current_assets',
        accounts: nonCurrentAssets,
        groupTotal:
            nonCurrentAssets.fold(0.0, (total, item) => total + item.amount),
      ));
    }

    return groups;
  }

  /// Create liability groups with current/non-current classification
  Future<List<BSAccountGroup>> _createLiabilityGroups(
    List<ChartOfAccountsModel> liabilityAccounts,
    DateTime asOfDate,
    bool includeZeroBalances,
  ) async {
    final groups = <BSAccountGroup>[];

    // Group by account type
    final currentLiabilities = <BSAccountItem>[];
    final nonCurrentLiabilities = <BSAccountItem>[];

    for (final account in liabilityAccounts) {
      final balance = await _calculateAccountBalance(account.id, asOfDate);

      if (!includeZeroBalances && balance == 0) continue;

      final accountItem = BSAccountItem(
        accountId: account.id,
        accountNumber: account.accountNumber,
        accountName: account.accountName,
        amount: balance,
      );

      // Classify as current or non-current based on account type
      if (_isCurrentLiability(account.accountType)) {
        currentLiabilities.add(accountItem);
      } else {
        nonCurrentLiabilities.add(accountItem);
      }
    }

    // Create groups
    if (currentLiabilities.isNotEmpty) {
      groups.add(BSAccountGroup(
        groupName: 'Current Liabilities',
        groupType: 'current_liabilities',
        accounts: currentLiabilities,
        groupTotal:
            currentLiabilities.fold(0.0, (total, item) => total + item.amount),
      ));
    }

    if (nonCurrentLiabilities.isNotEmpty) {
      groups.add(BSAccountGroup(
        groupName: 'Non-Current Liabilities',
        groupType: 'non_current_liabilities',
        accounts: nonCurrentLiabilities,
        groupTotal: nonCurrentLiabilities.fold(
            0.0, (total, item) => total + item.amount),
      ));
    }

    return groups;
  }

  /// Create equity groups
  Future<List<BSAccountGroup>> _createEquityGroups(
    List<ChartOfAccountsModel> equityAccounts,
    DateTime asOfDate,
    bool includeZeroBalances,
  ) async {
    final groups = <BSAccountGroup>[];
    final equityItems = <BSAccountItem>[];

    for (final account in equityAccounts) {
      final balance = await _calculateAccountBalance(account.id, asOfDate);

      if (!includeZeroBalances && balance == 0) continue;

      equityItems.add(BSAccountItem(
        accountId: account.id,
        accountNumber: account.accountNumber,
        accountName: account.accountName,
        amount: balance,
      ));
    }

    if (equityItems.isNotEmpty) {
      groups.add(BSAccountGroup(
        groupName: 'Owner\'s Equity',
        groupType: 'equity',
        accounts: equityItems,
        groupTotal: equityItems.fold(0.0, (total, item) => total + item.amount),
      ));
    }

    return groups;
  }

  /// Calculate account balance as of specific date using proper accounting principles
  Future<double> _calculateAccountBalance(
      String accountId, DateTime asOfDate) async {
    try {
      final user = _auth.currentUser;
      if (user == null) return 0.0;

      // Get account information to determine account type
      final accountDoc = await _firestore
          .collection('chart_of_accounts')
          .where('id', isEqualTo: accountId)
          .where('uid', isEqualTo: user.uid)
          .limit(1)
          .get();

      if (accountDoc.docs.isEmpty) {
        log('Account not found: $accountId');
        return 0.0;
      }

      final accountData = accountDoc.docs.first.data();
      final account = ChartOfAccountsModel.fromJson(accountData);

      final entriesQuery = _firestore
          .collection('journal_entries')
          .where('uid', isEqualTo: user.uid)
          .where('entryDate', isLessThanOrEqualTo: Timestamp.fromDate(asOfDate))
          .where('status', isEqualTo: 'posted'); // Only include posted entries

      final entriesSnapshot = await entriesQuery.get();
      double balance = 0.0;

      for (final doc in entriesSnapshot.docs) {
        final entry = JournalEntryModel.fromJson({
          'id': doc.id,
          ...doc.data(),
        });

        for (final lineItem in entry.lines) {
          if (lineItem.accountId == accountId) {
            // Calculate balance change using proper accounting principles
            final balanceChange =
                AccountTypeHelperService.calculateBalanceChange(
              accountType: account.accountType,
              debitAmount: lineItem.debitAmount,
              creditAmount: lineItem.creditAmount,
            );
            balance += balanceChange;
          }
        }
      }

      return balance;
    } catch (e) {
      log('Error calculating account balance: $e');
      return 0.0;
    }
  }

  /// Check if account type is current asset
  bool _isCurrentAsset(AccountType accountType) {
    return [
      AccountType.cash,
      AccountType.bank,
      AccountType.accountsReceivable,
      AccountType.inventory,
      AccountType.currentAssets,
    ].contains(accountType);
  }

  /// Check if account type is current liability
  bool _isCurrentLiability(AccountType accountType) {
    return [
      AccountType.accountsPayable,
      AccountType.currentLiabilities,
    ].contains(accountType);
  }

  /// Generate unique report ID
  String _generateReportId() {
    return 'BS_${DateTime.now().millisecondsSinceEpoch}';
  }

  /// Save Balance Sheet report
  Future<Either<FailureObj, SuccessObj>> saveBalanceSheetReport(
      BalanceSheetReport report) async {
    try {
      await _firestore
          .collection('balance_sheet_reports')
          .doc(report.reportId)
          .set(report.toJson());

      return Right(
          SuccessObj(message: 'Balance Sheet report saved successfully'));
    } catch (e) {
      log('Error saving Balance Sheet report: $e');
      return Left(FailureObj(
        code: 'save-error',
        message: 'Failed to save Balance Sheet report: $e',
      ));
    }
  }

  /// Get saved Balance Sheet reports
  Future<Either<FailureObj, List<BalanceSheetReport>>>
      getSavedBalanceSheetReports() async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return Left(FailureObj(
          code: 'user-not-authenticated',
          message: 'User not authenticated',
        ));
      }

      final snapshot = await _firestore
          .collection('balance_sheet_reports')
          .where('uid', isEqualTo: user.uid)
          .orderBy('generatedAt', descending: true)
          .get();

      final reports = snapshot.docs
          .map((doc) => BalanceSheetReport.fromJson(doc.data()))
          .toList();

      return Right(reports);
    } catch (e) {
      log('Error getting saved Balance Sheet reports: $e');
      return Left(FailureObj(
        code: 'fetch-error',
        message: 'Failed to fetch saved Balance Sheet reports: $e',
      ));
    }
  }

  /// Get specific Balance Sheet report
  Future<Either<FailureObj, BalanceSheetReport>> getBalanceSheetReport(
      String reportId) async {
    try {
      final doc = await _firestore
          .collection('balance_sheet_reports')
          .doc(reportId)
          .get();

      if (!doc.exists) {
        return Left(FailureObj(
          code: 'report-not-found',
          message: 'Balance Sheet report not found',
        ));
      }

      final report = BalanceSheetReport.fromJson(doc.data()!);
      return Right(report);
    } catch (e) {
      log('Error getting Balance Sheet report: $e');
      return Left(FailureObj(
        code: 'fetch-error',
        message: 'Failed to fetch Balance Sheet report: $e',
      ));
    }
  }

  /// Delete Balance Sheet report
  Future<Either<FailureObj, SuccessObj>> deleteBalanceSheetReport(
      String reportId) async {
    try {
      await _firestore
          .collection('balance_sheet_reports')
          .doc(reportId)
          .delete();

      return Right(
          SuccessObj(message: 'Balance Sheet report deleted successfully'));
    } catch (e) {
      log('Error deleting Balance Sheet report: $e');
      return Left(FailureObj(
        code: 'delete-error',
        message: 'Failed to delete Balance Sheet report: $e',
      ));
    }
  }
}
