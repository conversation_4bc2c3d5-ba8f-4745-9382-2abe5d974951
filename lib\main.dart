import 'dart:developer';

import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/bindings/app_bindings.dart';
import 'package:logestics/core/router/app_routes.dart';
import 'package:logestics/core/router/route_guard.dart';
import 'package:logestics/core/utils/app_constants/colors/app_colors.dart';
import 'package:logestics/firebase_options.dart';
import 'package:provider/provider.dart';

import 'features/home/<USER>/theme.dart';
import 'core/utils/widgets/scrolling.dart';

late ColorNotifier notifier;
void main() async {
  try {
    WidgetsFlutterBinding.ensureInitialized();

    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );

    await AppDependencyInjection().init();

    runApp(const MyApp());
  } catch (e) {
    log('Error during initialization: $e');
    runApp(MaterialApp(
      home: Scaffold(
        body: Center(
          child: Text('Error initializing app: $e'),
        ),
      ),
    ));
  }
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(
          create: (context) => ColorNotifier(),
        ),
      ],
      child: Builder(builder: (context) {
        var notifier = Provider.of<ColorNotifier>(context, listen: true);
        return GetMaterialApp(
          initialRoute: AppRoutes.root,
          initialBinding: AppBindings(),
          theme: ThemeData(
            appBarTheme: AppBarTheme(
              scrolledUnderElevation: 0,
              elevation: 0,
              iconTheme: IconThemeData(color: notifier.text),
              backgroundColor: AppColors.primary,
            ),
            scaffoldBackgroundColor: AppColors.surface,
            dialogTheme: DialogThemeData(
              backgroundColor: AppColors.surface,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            primaryColor: AppColors.primary,
            fontFamily: "Outfit",
            splashColor: Colors.transparent,
            highlightColor: Colors.transparent,
            inputDecorationTheme: InputDecorationTheme(
              filled: true,
              fillColor: AppColors.surface,
              border: OutlineInputBorder(
                borderSide: BorderSide(color: AppColors.onSurfaceVariant),
              ),
              enabledBorder: OutlineInputBorder(
                borderSide: BorderSide(color: AppColors.onSurfaceVariant),
              ),
              focusedBorder: OutlineInputBorder(
                borderSide: BorderSide(color: AppColors.primary),
              ),
            ),
            elevatedButtonTheme: ElevatedButtonThemeData(
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: AppColors.onPrimary,
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
            cardTheme: CardThemeData(
              elevation: 2,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
          // darkTheme: AppTheme.darkTheme(MediaQuery.of(context).devicePixelRatio),
          // themeMode: ThemeMode.system,
          getPages: AppRoutes.getPages,
          unknownRoute: GetPage(
            name: '/not-found',
            page: () => const UnknownRoutePage(),
          ),
          defaultTransition: Transition.fade,
          debugShowCheckedModeBanner: false,
          scrollBehavior: MyCustomScrollerBehavior(),
          builder: (context, child) {
            return child ?? SizedBox();
          },
        );
      }),
    );
  }
}
